// commitlint.config.js
export default {
    extends: ['@commitlint/config-conventional'],
    rules: {
      'type-enum': [
        2,
        'always',
        [
          'feat',
          'fix',
          'chore',
          'docs',
          'style',
          'refactor',
          'test',
          'perf',
          'ci',
          'build',
          'revert',
          'deps'
        ],
      ],
      'scope-empty': [2, 'never'],
      'subject-empty': [2, 'never'],
      'type-case': [2, 'always', 'lower-case'],
      'scope-case': [2, 'always', 'lower-case'],
      'subject-case': [0],
    },
  };
  