# Git Hooks with <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>

We use [<PERSON><PERSON>](https://typicode.github.io/husky) and [<PERSON>mmitlint](https://commitlint.js.org/) to enforce consistent commit messages and prevent commits with linting errors.

---

## Setup

- <PERSON><PERSON> manages Git hooks inside the `.husky/` directory.   
- `commitlint` validates commit messages against our rules.

---

## Hooks in Use

### commit-msg
Runs `commitlint` to enforce commit message conventions:

- **Allowed types**:  
  `feat`, `fix`, `chore`, `docs`, `style`, `refactor`, `test`, `perf`, `ci`, `build`, `revert`, `deps`
- **Subject rules**:  
  - Subject must be lowercase, but acronyms like `PR`, `API`, `CI` are allowed.  
  - `subject-case` is disabled to allow this flexibility.
- **Scope rules**:
  - Scope must be lowercase, have no spaces (use a single word or hyphen-separated words), and be short and meaningful (describe the area affected).

---

## Commit Message Format

We follow [Conventional Commits](https://www.conventionalcommits.org/):

`<type>(<scope>): <subject>` 

**Examples**:
- `feat(auth): add jwt token refresh`
- `fix(ui): correct navbar alignment`
- `chore(deps): update eslint to latest version`