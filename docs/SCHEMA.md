# Database Schema and Migrations

This document provides an overview of the database schema, how to run migrations, and how to inspect the database state.

## Running Migrations

To set up or update the database schema, run the migration script. This script will apply any pending migrations.

```bash
./script/migration.sh
```

### Advanced Migration Control

The `migration.sh` script allows for more granular control over migrations. You can specify the direction (`up` or `down`) and the number of migrations to apply.

-   **`up`**: Applies pending migrations.
-   **`down`**: Reverts the last applied migrations.

**To run all pending migrations (default behavior):**

```bash
./script/migration.sh
```

**To apply a specific number of migrations:**

```bash
# Apply the next 2 pending migrations
./script/migration.sh up 2
```

**To revert the last migration:**

```bash
./script/migration.sh down 1
```

**To revert a specific number of migrations:**

```bash
# Revert the last 3 migrations
./script/migration.sh down 3
```

**To revert all migrations:**

```bash
./script/migration.sh down all
```

## Tables

### `users`

The `users` table stores user information.

-   `id` (UUID, Primary Key): A unique identifier for each user.
-   `user_data` (JSONB): A JSONB column to store user-related data.
    -   `phone_number` (string): The user's phone number. This is indexed for uniqueness.

**Example `user_data`:**

```json
{
  "phone_number": "+1234567890",
  "name": "John Doe"
}
```

### `otp`

The `otp` table stores One-Time Passwords for user verification.

-   `id` (UUID, Primary Key): A unique identifier for each OTP entry.
-   `otp_data` (JSONB): A JSONB column to store OTP-related data. A GIN index is applied to this column for efficient querying.

## Verifying Tables with `psql`

You can use the `psql` command-line tool to connect to your PostgreSQL database and inspect the tables.

#### 1. Ensure psql is installed
On Ubuntu/Debian:
```bash
sudo apt install postgresql-client -y
```
On other distributions:
```bash
brew install libpq
brew link --force libpq
```
On Windows: Install PostgreSQL from https://www.postgresql.org/download/ which includes `psql`

1.  **Connect to the database:**

    ```bash
    psql -h localhost -p 5433 -U migration_user -d rishiflow
    ```

2.  **List all tables:**

    ```sql
    \dt
    ```

    This should show the `users` and `otp` tables.

3.  **Describe a table:**

    To see the columns and types for the `users` table:

    ```sql
    \d users
    ```

    To see the columns and types for the `otp` table:

    ```sql
    \d otp
    ```
