# Database Schema and Migration Guide

This document provides comprehensive information about the database schema, migration system, and how to manage database changes in the Rishi Core project.

## Migration Script Usage

The migration script (`script/migration.sh`) is the primary tool for managing database schema changes.

### Basic Syntax

```bash
./script/migration.sh [DIRECTION] [COUNT|FILE] [OPTIONS]
```
or 

```bash
./dev migration [DIRECTION] [COUNT|FILE] [OPTIONS]
```

### Parameters

#### DIRECTION
Migration direction (default: `up`)
- `up` - Apply migrations (create/modify schema)
- `down` - Rollback migrations (undo schema changes)

#### COUNT
Number of migrations to run (default: `Infinity` for all pending)
- Can be a number (`1`, `2`, `3`...)
- Can be `Infinity` to run all pending migrations

#### FILE
Specific migration file to run using partial name matching
- Examples: `create-users`, `otp-table`, `1678886400000`
- The script will search for migration files containing the provided string

### Options

- `--help`, `-h` - Show detailed help message

### Usage Examples
```bash
# Run all pending migrations
./script/migration.sh
    or
./dev migration
       
# Run 1 migration up
./script/migration.sh up 1
    or
./dev migration up 1

# Run 2 migrations down
./script/migration.sh down 2
    or
./dev migration down 2

# Rollback 2 migrations
./script/migration.sh down 2
    or
./dev migration down 2

# Run specific migration by partial name
./script/migration.sh up create-users
    or
./dev migration up create-users

# Rollback specific migration
./script/migration.sh down otp-table
    or
./dev migration down otp-table

# Run migration by timestamp
./script/migration.sh up 1678886400000
    or
./dev migration up 1678886400000

# Show help
./script/migration.sh --help
    or
./dev migration --help
```

## Environment Requirements

The migration script requires a `.env` file with the following variables:

- `DB_MIGRATION_USER` - Database user with schema modification privileges
- `DB_MIGRATION_PASSWORD` - Password for the migration user
- `DB_HOST` - Database host (usually `localhost`)
- `DB_PORT` - Database port (usually `5433`)
- `DB_NAME` - Database name (usually `rishiflow`)

## Current Database Schema

### Tables

#### users
Stores user information in JSONB format for flexibility.

```sql
CREATE TABLE users (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_data jsonb NOT NULL
);
```

**Indexes:**
- Primary key on `id`
- Unique index on `phone_number` within JSONB: `((user_data->>'phone_number'))`

**Purpose:** Stores user profiles, authentication data, and other user-related information in a flexible JSONB structure.

#### otp
Stores one-time password data for authentication.

```sql
CREATE TABLE otp (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    otp_data jsonb NOT NULL
);
```

**Indexes:**
- Primary key on `id`
- GIN index on `otp_data` for efficient JSONB querying

**Purpose:** Manages OTP generation, validation, and expiration for user authentication flows.

#### pgmigrations
System table that tracks which migrations have been applied.

```sql
CREATE TABLE pgmigrations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    run_on TIMESTAMP NOT NULL
);
```

**Purpose:** Automatically managed by `node-pg-migrate` to track migration history.

## Migration Files

Migration files are located in the `migrations/` directory and follow the naming convention:
`[timestamp]_[description].ts`

### Current Migration Files

1. **1678886400000_create-users-table.ts**
   - Creates the `users` table
   - Adds unique index on phone number

2. **1678886400001_create-otp-table.ts**
   - Creates the `otp` table
   - Adds GIN index for JSONB querying

### Creating New Migrations

To create a new migration file:

```bash
npm run migrate:create -- your-migration-name
```

This creates a new timestamped migration file in the `migrations/` directory.

### Migration File Structure

```typescript
import { MigrationBuilder } from 'node-pg-migrate';

export async function up(pgm: MigrationBuilder): Promise<void> {
    // Schema changes to apply
    pgm.createTable('example', {
        id: { type: 'uuid', primaryKey: true, default: pgm.func('gen_random_uuid()') },
        data: { type: 'jsonb', notNull: true }
    });
}

export async function down(pgm: MigrationBuilder): Promise<void> {
    // Rollback changes
    pgm.dropTable('example');
}
```


## Important Note on Migration Status
when you ran `./script/migration.sh` and get this message:
```bash
No migrations to run!
Migrations complete!

```
 This simply means that all available migrations have already been applied to the database.

- Migrations are tracked in the `pgmigrations` table, which stores the name and timestamp of every migration that has been executed.

- When you run the script, it compares the available migration files in migrations/ with the entries in pgmigrations. If a file is already recorded there, it won’t be applied again.

This ensures migrations are `idempotent` (safe to run multiple times without duplicating schema changes).

## Best Practices

### Migration Design
- **One change per migration** - Keep migrations focused on a single logical change
- **Always include rollback** - Implement the `down` function for every migration
- **Test rollbacks** - Ensure down migrations work correctly
- **Use transactions** - Migrations run in transactions by default

### Schema Design
- **Use UUIDs for primary keys** - Provides better scalability and security
- **Leverage JSONB** - For flexible, schema-less data storage
- **Add appropriate indexes** - Especially for JSONB queries and foreign keys
- **Consider data migration** - Plan for data transformation when changing schema

### File Management
- **Descriptive names** - Use clear, descriptive migration names
- **Sequential execution** - Migrations run in timestamp order
- **Never modify existing migrations** - Once applied, create new migrations for changes

## Troubleshooting

### Common Issues

1. **Environment variables not loaded**
   - Ensure `.env` file exists and contains required variables
   - Check file permissions and format

2. **Database connection failed**
   - Verify database container is running: `podman ps | grep rishiflow`
   - Check connection parameters in `.env`
   - Test connection: `podman exec rishiflow-db psql -U migration_user -d rishiflow -c "SELECT NOW();"`

3. **Migration file not found**
   - Check file exists in `migrations/` directory
   - Verify filename matches the pattern used in the script
   - Use `ls migrations/` to see available files

4. **Permission denied**
   - Ensure migration user has proper privileges
   - Check database user permissions
   - Verify schema modification rights

### Getting Help

- Run `./script/migration.sh --help` for usage information
- Check migration status: `./script/migration.sh up 0` (dry run)
- List available migrations: `ls -la migrations/`
- Check database tables: `podman exec rishiflow-db psql -U migration_user -d rishiflow -c "\dt"`

## Integration with Application

The application uses different database users for different purposes:

- **migration_user** - Schema modifications (CREATE, ALTER, DROP)
- **app_user** - Data operations (SELECT, INSERT, UPDATE, DELETE)
- **rishiflow** - Database owner (full privileges)
- **postgres** - System administrator (PostgreSQL superuser)

This separation ensures security and proper privilege management across different application layers.
