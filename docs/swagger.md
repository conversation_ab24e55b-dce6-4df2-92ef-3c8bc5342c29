# Swagger API Documentation

This document provides instructions on how to access and use the Swagger UI for this project.

## How to Access

1. **Start the application:**
   
   ```bash
   npm run build && npm run start
   ```

2. **Open your web browser and navigate to:**
   
   [http://localhost:3000/docs](http://localhost:3000/docs)

## How to Use

The Swagger UI provides a user-friendly interface to explore and interact with the API.

1. **Explore Endpoints:** A list of all available API endpoints will be displayed.
2. **View Details:** Click on an endpoint to view its details, including:
   * HTTP method (GET, POST, etc.)
   * Request parameters
   * Response format
3. **Test Endpoints:**
   * Click the "Try it out" button.
   * Fill in the required parameters.
   * Click the "Execute" button to send a request to the endpoint.
   * The response from the server will be displayed below.
