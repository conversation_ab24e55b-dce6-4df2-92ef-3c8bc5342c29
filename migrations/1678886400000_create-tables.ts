import { MigrationBuilder } from 'node-pg-migrate';

export async function up(pgm: MigrationBuilder): Promise<void> {
    pgm.createTable('users', {
        id: {
            type: 'uuid',
            primaryKey: true,
            default: pgm.func('gen_random_uuid()')
        },
        user_data: {
            type: 'jsonb',
            notNull: true
        }
    });

    pgm.createTable('otp', {
        id: {
            type: 'uuid',
            primaryKey: true,
            default: pgm.func('gen_random_uuid()')
        },
        otp_data: {
            type: 'jsonb',
            notNull: true
        }
    });

    // Create a GIN index on the otp_data JSONB column for efficient querying of keys.
    pgm.createIndex('otp', 'otp_data', { method: 'gin' });

    // Create a unique index on the 'phone_number' key within the user_data JSONB.
    // This enforces data integrity by preventing multiple users with the same phone number.
    pgm.createIndex('users', "((user_data->>'phone_number'))", { unique: true });
}

export async function down(pgm: MigrationBuilder): Promise<void> {
    // Drop indexes first, as they depend on the tables.
    pgm.dropIndex('users', "((user_data->>'phone_number'))");
    pgm.dropIndex('otp', 'otp_data');

    // Then, drop the tables.
    pgm.dropTable('otp');
    pgm.dropTable('users');
}
