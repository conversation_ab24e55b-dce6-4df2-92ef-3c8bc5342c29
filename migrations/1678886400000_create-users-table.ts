import { MigrationBuilder } from 'node-pg-migrate';

export async function up(pgm: MigrationBuilder): Promise<void> {
  pgm.createTable('users', {
    id: {
      type: 'uuid',
      primaryKey: true,
      default: pgm.func('gen_random_uuid()'),
    },
    user_data: {
      type: 'jsonb',
      notNull: true,
    },
  });

  // Create a unique index on the 'phone_number' key within the user_data JSONB.
  // This enforces data integrity by preventing multiple users with the same phone number.
  pgm.createIndex('users', "((user_data->>'phone_number'))", { unique: true });
}

export async function down(pgm: MigrationBuilder): Promise<void> {
  // Drop index first, as it depends on the table.
  pgm.dropIndex('users', "((user_data->>'phone_number'))");

  // Then, drop the table.
  pgm.dropTable('users');
}
