import { MigrationBuilder } from 'node-pg-migrate';

export async function up(pgm: MigrationBuilder): Promise<void> {
    pgm.createTable('otp', {
        id: {
            type: 'uuid',
            primaryKey: true,
            default: pgm.func('gen_random_uuid()')
        },
        otp_data: {
            type: 'jsonb',
            notNull: true
        }
    });

    // Create a GIN index on the otp_data JSONB column for efficient querying of keys.
    pgm.createIndex('otp', 'otp_data', { method: 'gin' });
}

export async function down(pgm: MigrationBuilder): Promise<void> {
    // Drop index first, as it depends on the table.
    pgm.dropIndex('otp', 'otp_data');
    
    // Then, drop the table.
    pgm.dropTable('otp');
}
