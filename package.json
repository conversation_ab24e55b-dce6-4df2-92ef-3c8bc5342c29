{"name": "rishi-core", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"test": "jest --config jest.config.ts --detect<PERSON><PERSON><PERSON><PERSON>les", "dev": "tsx watch src/server.ts", "build": "npm run typecheck && tsc", "typecheck": "tsc --noEmit", "start": "node dist/src/server.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "check": "npm run typecheck && npm run lint && npm run format:check", "fix": "npm run lint:fix && npm run format", "prepare": "husky", "migrate": "node-pg-migrate -m migrations -d src/database/dbmigrate.ts up", "migrate:create": "ts-node -P tsconfig.json ./node_modules/.bin/node-pg-migrate create --migrations-dir ./migrations", "migrate:up": "ts-node -P tsconfig.json ./node_modules/.bin/node-pg-migrate up", "migrate:down": "ts-node -P tsconfig.json ./node_modules/.bin/node-pg-migrate down"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/babel__core": "^7.20.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.11.24", "@types/pg": "^8.11.2", "@types/pino": "^7.0.4", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.1.7", "jest": "^30.1.2", "jest-util": "^30.0.5", "lint-staged": "^16.1.6", "prettier": "^3.2.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "tsx": "^4.7.1", "typescript": "^5.3.3"}, "dependencies": {"dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "node-pg-migrate": "^8.0.3", "pg": "^8.11.3", "pg-boss": "^10.3.2", "pino": "^8.21.0", "pino-pretty": "^10.3.1", "supertest": "^6.3.4", "ts-jest": "^29.1.2"}}