#!/bin/bash
set -euo pipefail

# Ensure .env file exists
if [ ! -f .env ]; then
  echo "❌ Error: .env file not found. Please create it with DB_MIGRATION_USER, DB_MIGRATION_PASSWORD, DB_HOST, DB_PORT, DB_NAME."
  exit 1
fi

# Load environment variables from .env
export $(grep -v '^#' .env | xargs)

# Verify required variables are set
REQUIRED_VARS=(DB_MIGRATION_USER DB_MIGRATION_PASSWORD DB_HOST DB_PORT DB_NAME)

for VAR in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!VAR:-}" ]; then
    echo "❌ Error: $VAR is not set in .env"
    exit 1
  fi
done

# Construct DATABASE_URL
export DATABASE_URL="postgres://${DB_MIGRATION_USER}:${DB_MIGRATION_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Migration direction and count (default: up Infinity)
DIRECTION=${1:-"up"}
COUNT=${2:-"Infinity"}

echo "📦 Running migrations with user=$DB_MIGRATION_USER on db=$DB_NAME at $DB_HOST:$DB_PORT"
npx node-pg-migrate "$DIRECTION" -c "$COUNT" -d migrations --migrations-table pgmigrations --verbose
