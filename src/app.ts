import express, { Application } from 'express';
import router from './routes/routes.js';
import helmet from 'helmet';
import LOGGER from './utils/logger.js';
import { AppError, InternalServerError } from './utils/errors.js';

export const createApp = (): Application => {
  // Initialize Express application
  const app = express();

  // Global middleware
  app.use(helmet()); // Set security-related HTTP headers
  app.use(express.json()); // Parse incoming JSON requests into req.body
  app.disable('x-powered-by'); // Hide "X-Powered-By" header to avoid exposing server details

  // Register routes
  app.use('/', router);

  // Centralized error handler
  app.use(
    (err: Error, _req: express.Request, res: express.Response, next: express.NextFunction) => {
      LOGGER.error(err.stack);

      if (err instanceof AppError) {
        res.status(err.statusCode).json({ message: err.message });
      } else {
        const internalError = new InternalServerError();
        res.status(internalError.statusCode).json({ message: internalError.message });
      }

      next(err);
    },
  );

  return app;
};
