import twilio from 'twilio';
// --- Configuration ---
const accountSid: string = process.env.TWILIO_AUTH_SID as string;
const authToken: string = process.env.TWILIO_AUTH_TOKEN as string;

// Your Twilio Sandbox WhatsApp number (the 'from' number)
const twilioSandboxNumber: string = process.env.TWILIO_SANDBOX_NUMBER as string;
const client = twilio(accountSid, authToken);
const sendTwilioOTPSms = async (recipient: string, otp: string) => {
  try {
    await client.messages.create({
      body: `Your verification code is ${otp}`,
      from: `whatsapp:${twilioSandboxNumber}`,
      to: `whatsapp:${recipient}`,
    });
  } catch (err) {
    if (err instanceof Error) {
      throw err; // preserve stack trace
    }
    throw new Error(String(err));
  }
};
export default sendTwilioOTPSms;
