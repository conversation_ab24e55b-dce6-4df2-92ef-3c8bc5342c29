import { Request, Response } from 'express';
import { createUser, insertOTP, retrieveValidOTP } from '../database/queries.js';
import generateOTP from '../utils/otp-generator.js';
import { UserData } from '../types/user.js';
import { OtpData } from '../types/otp.js';
import isValidPhoneNumber from '../utils/phone-number-validator.js';

/**
 * @openapi
 * /register:
 *   post:
 *     summary: Register a user using phone number and send OTP
 *     tags: [User]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone_number
 *             properties:
 *               phone_number:
 *                 type: string
 *                 description: The user's phone number (must be in +91XXXXXXXXXX format).
 *             example:
 *               phone_number: "+911234567890"
 *     responses:
 *       200:
 *         description: OTP was successfully generated, or a valid OTP already exists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               oneOf:
 *                 - properties:
 *                     message:
 *                       type: string
 *                       example: "Your OTP has been generated and sent to your whatsapp number"
 *                 - properties:
 *                     message:
 *                       type: string
 *                       example: "An active OTP has already been sent. Please wait before requesting a new one."
 *       400:
 *         description: Invalid phone number format.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid phone number format. Must be +91 followed by 10 digits."
 *       500:
 *         description: Internal Server Error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
const userRegistration = async (req: Request, res: Response) => {
  const { phone_number } = req.body;
  if (!isValidPhoneNumber(phone_number)) {
    res
      .status(400)
      .json({ error: 'Invalid phone number format. Must be +91 followed by 10 digits.' });
    return;
  }

  try {
    // First, check if a valid OTP already exists for this number
    const existingOtp = await retrieveValidOTP(phone_number);
    if (existingOtp) {
      return res.status(200).json({
        message: 'An active OTP has already been sent. Please wait before requesting a new one.',
      });
    }

    // If no valid OTP exists, proceed to generate a new one
    const otp = generateOTP();
    const now = new Date();
    const expires = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now

    const userData: UserData = { phone_number: phone_number, first_name: '', last_name: '' };
    const otpData: OtpData = {
      phone_number,
      otp_code: otp,
      created_at: now.toISOString(),
      expires_at: expires.toISOString(),
    };

    // Attempt to create a new user.
    // If the user already exists (unique constraint violation), we ignore the error and proceed.
    try {
      await createUser(userData);
    } catch (err: any) {
      // PostgreSQL unique_violation error code
      if (err.code !== '23505') {
        throw err; // Re-throw if it's not the error we expect
      }
    }

    // Insert the new OTP for the user.
    await insertOTP(otpData);

    // Send OTP Logic here via whatsapp
    res
      .status(200)
      .json({ message: 'Your OTP has been generated and sent to your whatsapp number' });
  } catch {
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

export default userRegistration;
