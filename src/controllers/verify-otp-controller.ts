import { Request, Response } from 'express';
import { retrieveValidOTP } from '../database/queries.js';
import LOGGER from '../utils/logger.js';

/**
 * @openapi
 * /verify-otp:
 *   post:
 *     summary: Verify OTP after user registration
 *     tags: [User]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone_number
 *               - otp
 *             properties:
 *               phone_number:
 *                 type: string
 *                 description: The user's phone number (must be in +91XXXXXXXXXX format).
 *               otp:
 *                 type: string
 *                 description: The OTP received by the user.
 *             example:
 *               phone_number: "+911234567890"
 *               otp: "123456"
 *     responses:
 *       200:
 *         description: <PERSON><PERSON> successfully verified and user authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Authentication successful.
 *       400:
 *         description: Bad request — missing phone number, OTP, malformed JSON, or invalid/expired OTP.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *             examples:
 *               missingFields:
 *                 value:
 *                   error: "Phone number and OTP are required."
 *               invalidOtp:
 *                 value:
 *                   error: "Invalid or expired OTP."
 *               malformedJson:
 *                 value:
 *                   error: "Malformed JSON in request body"
 *       500:
 *         description: Internal server error during OTP verification.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

const verifyOTP = async (req: Request, res: Response) => {
  const { phone_number, otp } = req.body;

  if (!phone_number || !otp) {
    return res.status(400).json({ error: 'Phone number and OTP are required.' });
  }

  try {
    const storedOtpData = await retrieveValidOTP(phone_number);

    if (!storedOtpData || otp !== storedOtpData.otp_data.otp_code) {
      return res.status(400).json({ error: 'Invalid or expired OTP.' });
    }

    // JWT Implementation here
    return res.status(200).json({ message: 'Authentication successful.' });
  } catch (err) {
    // It's good practice to log the actual error on the server
    LOGGER.error('Error during OTP verification:', err);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

export default verifyOTP;
