import { Pool, PoolClient } from 'pg';
import { CONFIG } from '../../config/index.js';
import LOGGER from '../utils/logger.js';
import tables from './tables.js';

class Database {
  private static instance: Database;
  private pool: Pool;

  private constructor() {
    this.pool = new Pool({
      host: CONFIG.MainDatabase.host,
      port: CONFIG.MainDatabase.port,
      user: CONFIG.ApplicationUser.user,
      password: CONFIG.ApplicationUser.password,
      database: CONFIG.MainDatabase.name,
    });
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public async testConnection(): Promise<void> {
    let client: PoolClient | undefined;
    try {
      client = await this.pool.connect();
      const result = await client.query('SELECT NOW()');
      LOGGER.info('Database connection successful at:', result.rows[0].now);
    } catch (err) {
      LOGGER.error(
        'Database connection error. Please check config.yaml and .env files and ensure the database container is running.',
      );
      throw err;
    } finally {
      client?.release();
    }
  }

  public async createTables(): Promise<void> {
    let client: PoolClient | undefined;
    try {
      client = await this.pool.connect();
      for (const table of tables) {
        await client.query(table);
      }
      LOGGER.info('Tables created successfully or already exist.');
    } catch (err) {
      LOGGER.error('Error creating tables:', err);
      throw err;
    } finally {
      client?.release();
    }
  }

  public getPool(): Pool {
    return this.pool;
  }
}

const db = Database.getInstance();
export default db;
