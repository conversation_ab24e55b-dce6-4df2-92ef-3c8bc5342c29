import { OtpData } from '../types/otp.js';
import { UserData } from '../types/user.js';
import db from './db-connection.js';

/**
 * Inserts a new user into the users table.
 * @param data The user data to insert.
 * @returns A promise that resolves with the query result.
 */
export const createUser = async (data: UserData) => {
  const query = `INSERT INTO users (user_data) VALUES ($1)`;
  // Note: The data object must be passed inside an array for the driver.
  return db.getPool().query(query, [data]);
};

/**
 * Inserts a new OTP record into the otp table.
 * @param data The OTP data to insert.
 * @returns A promise that resolves with the query result.
 */
export const insertOTP = async (data: OtpData) => {
  const query = `INSERT INTO otp (otp_data) VALUES ($1)`;
  return db.getPool().query(query, [data]);
};

/**
 * Retrieves the latest valid OTP for a given phone number.
 * @param phoneNumber The phone number to look up.
 * @returns A promise that resolves with the OTP data or null if not found.
 */
export const retrieveValidOTP = async (
  phoneNumber: string,
): Promise<{ id: string; otp_data: OtpData } | null> => {
  // This query now searches inside the JSONB for the phone_number.
  // It also checks that the OTP has not expired.
  const query = `
    SELECT id, otp_data FROM otp
     WHERE
     otp_data->>'phone_number' = $1
      AND (otp_data->>'expires_at')::timestamptz > NOW()
    ORDER BY
       (otp_data->>'created_at')::timestamptz DESC
     LIMIT 1
   `;
  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  // The result row will have an id and a single field `otp_data` containing the object
  return result.rows[0] || null;
};

/**
 * Deletes an OTP record by its ID.
 * @param id The ID of the OTP record to delete.
 * @returns A promise that resolves when the operation is complete.
 */
export const deleteOTPById = async (id: string): Promise<void> => {
  const query = `DELETE FROM otp WHERE id = $1`;
  await db.getPool().query(query, [id]);
};
