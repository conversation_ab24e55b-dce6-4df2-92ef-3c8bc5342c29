import { OtpData } from '../types/otp.js';
import { UserData } from '../types/user.js';
import db from './db-connection.js';

// Add user to users table
export const createUser = async (data: UserData) => {
  const query = `INSERT INTO users (user_data) VALUES ($1)`;
  // Note: The data object must be passed inside an array for the driver.
  return db.getPool().query(query, [data]);
};

export const insertOTP = async (data: OtpData) => {
  const query = `INSERT INTO otp (otp_data) VALUES ($1)`;
  return db.getPool().query(query, [data]);
};

// Retrieve the latest valid OTP for a phone number
export const retrieveValidOTP = async (
  phoneNumber: string,
): Promise<{ id: string; otp_data: OtpData } | null> => {
  // This query now searches inside the JSONB for the phone_number.
  // It also checks that the OTP has not expired.
  const query = `
    SELECT id, otp_data FROM otp
     WHERE
     otp_data->>'phone_number' = $1
      AND (otp_data->>'expires_at')::timestamptz > NOW()
    ORDER BY
       (otp_data->>'created_at')::timestamptz DESC
     LIMIT 1
   `;
  const values = [phoneNumber];
  const result = await db.getPool().query(query, values);

  // The result row will have an id and a single field `otp_data` containing the object
  return result.rows[0] || null;
};

// Delete an OTP record by its primary key
export const deleteOTPById = async (id: string): Promise<void> => {
  const query = `DELETE FROM otp WHERE id = $1`;
  await db.getPool().query(query, [id]);
};
