import express from 'express';
import userRegistration from '../controllers/user-registration-controller.js';
import verifyOTP from '../controllers/verify-otp-controller.js';

const router = express.Router(); // Create a new router instance

// User registration route
router.post('/register', userRegistration);

// For verifying the OTP sent to the user
router.post('/verify-otp', verifyOTP);

export default router; // Export router for use in the main app (app.ts)
