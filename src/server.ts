import { createApp } from './app.js';
import { CONFIG } from '../config/index.js';
import LOGGER from './utils/logger.js';
import db from './database/db-connection.js';

const startServer = async () => {
  try {
    // Verify database connectivity before starting the server

    await db.testConnection();
    await db.createTables();

    // Initialize Express application
    const app = createApp();
    const { port, host } = CONFIG.Server;

    // Start the server
    const server = app.listen(port, host, () => {
      LOGGER.info(`🚀 Server running at http://${host}:${port}`);
    });

    // Handle server-level errors (e.g. port already in use)
    server.on('error', error => {
      LOGGER.error(`Server error: ${error}`);
      process.exit(1);
    });
  } catch (error) {
    // Log and exit if startup fails
    LOGGER.error(`❌ Failed to start server. Please check the logs above for details ${error}`);
    process.exit(1);
  }
};

startServer();
