/**
 * @file Defines the structure for One-Time Password (OTP) data.
 */

/**
 * Represents the structure of an OTP object.
 */
export interface OtpData {
  /** The phone number associated with the OTP, in international format. */
  phone_number: string;

  /** The generated 4-digit OTP code. */
  otp_code: string;

  /** The ISO 8601 timestamp of when the OTP was created. */
  created_at: string;

  /** The ISO 8601 timestamp of when the OTP will expire. */
  expires_at: string;
}
