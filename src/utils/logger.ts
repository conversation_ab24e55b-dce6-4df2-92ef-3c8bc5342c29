import { pino } from 'pino';

// Configure Pino logger with pretty-printing for development readability
const loggerConfig =
  process.env.NODE_ENV === 'test'
    ? {}
    : {
        transport: {
          target: 'pino-pretty', // Use pino-pretty for formatted console output
          options: {
            colorize: true, // Enable colored log levels
            translateTime: 'SYS:standard', // Show timestamps in system time
            ignore: 'pid,hostname', // Exclude process ID and hostname fields
          },
        },
      };

const LOGGER = pino(loggerConfig);

export default LOGGER;
