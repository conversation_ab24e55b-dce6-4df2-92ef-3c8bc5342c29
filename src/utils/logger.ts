import { pino } from 'pino';

// Configure Pino logger with pretty-printing for development readability
const LOGGER = pino({
  transport: {
    target: 'pino-pretty', // Use pino-pretty for formatted console output
    options: {
      colorize: true, // Enable colored log levels
      translateTime: 'SYS:standard', // Show timestamps in system time
      ignore: 'pid,hostname', // Exclude process ID and hostname fields
    },
  },
});

export default LOGGER;
