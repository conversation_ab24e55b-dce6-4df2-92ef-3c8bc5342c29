import { getRequiredEnv } from '../../../config/index.js';
import LOGGER from '../../../src/utils/logger.js';

// Mock the logger to prevent console output during tests and to spy on its methods.
jest.mock('../../../src/utils/logger.js', () => ({
  __esModule: true,
  default: {
    error: jest.fn(),
  },
}));

describe('Configuration: getRequiredEnv', () => {
  const originalEnv = { ...process.env };

  // Spy on process.exit and replace implementation with a no-op
  const mockExit = jest
    .spyOn(process, 'exit')
    // cleaner typing: cast jest.fn() to the right function signature
    .mockImplementation((() => {
      return undefined as never;
    }) as unknown as typeof process.exit);

  // Grab the mocked logger
  const mockLoggerError = LOGGER.error as unknown as jest.Mock;

  beforeEach(() => {
    process.env = { ...originalEnv };
    jest.clearAllMocks();
  });

  afterAll(() => {
    process.env = originalEnv;
    mockExit.mockRestore();
  });

  test('should return the environment variable value when it exists', () => {
    const key = 'MY_TEST_VAR';
    const value = 'hello world';
    process.env[key] = value;

    const result = getRequiredEnv(key);

    expect(result).toBe(value);
    expect(mockLoggerError).not.toHaveBeenCalled();
    expect(mockExit).not.toHaveBeenCalled();
  });

  test('should call logger.error and process.exit when environment variable is missing', () => {
    const key = 'MISSING_VAR';
    delete process.env[key];

    getRequiredEnv(key);

    expect(mockLoggerError).toHaveBeenCalledWith(
      `❌ Required environment variable ${key} is not set. The application will exit.`,
    );
    expect(mockExit).toHaveBeenCalledWith(1);
  });
});
