import { Request, Response } from 'express';
import userRegistration from '../../../src/controllers/user-registration-controller';
import * as queries from '../../../src/database/queries';
import * as otpGenerator from '../../../src/utils/otp-generator';
import * as phoneNumberValidator from '../../../src/utils/phone-number-validator';

// Mock the dependencies
jest.mock('../../../src/database/queries');
jest.mock('../../../src/utils/otp-generator');
jest.mock('../../../src/utils/phone-number-validator');

describe('User Registration Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let json: jest.Mock;
  let status: jest.Mock;

  beforeEach(() => {
    req = {
      body: {
        phone_number: '+254740707743',
      },
    };
    json = jest.fn();
    status = jest.fn(() => ({ json }));
    res = {
      status,
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return 400 if the phone number is invalid', async () => {
    (phoneNumberValidator.default as jest.Mock).mockReturnValue(false);

    await userRegistration(req as Request, res as Response);

    expect(status).toHaveBeenCalledWith(400);
    expect(json).toHaveBeenCalledWith({
      error: 'Invalid phone number format. Must be +91 followed by 10 digits.',
    });
  });

  it('should return 200 if a valid OTP already exists', async () => {
    (phoneNumberValidator.default as jest.Mock).mockReturnValue(true);
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue({ otp_code: '123456' });

    await userRegistration(req as Request, res as Response);

    expect(status).toHaveBeenCalledWith(200);
    expect(json).toHaveBeenCalledWith({
      message: 'An active OTP has already been sent. Please wait before requesting a new one.',
    });
  });

  it('should create a new user and OTP if none exists', async () => {
    (phoneNumberValidator.default as jest.Mock).mockReturnValue(true);
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    (otpGenerator.default as jest.Mock).mockReturnValue('654321');
    (queries.createUser as jest.Mock).mockResolvedValue(undefined);
    (queries.insertOTP as jest.Mock).mockResolvedValue(undefined);

    await userRegistration(req as Request, res as Response);

    expect(queries.createUser).toHaveBeenCalled();
    expect(queries.insertOTP).toHaveBeenCalled();
    expect(status).toHaveBeenCalledWith(200);
    expect(json).toHaveBeenCalledWith({
      message: 'Your OTP has been generated and sent to your whatsapp number',
    });
  });

  it('should handle existing user gracefully (unique constraint violation)', async () => {
    (phoneNumberValidator.default as jest.Mock).mockReturnValue(true);
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    (otpGenerator.default as jest.Mock).mockReturnValue('654321');
    // Simulate a unique constraint violation
    (queries.createUser as jest.Mock).mockRejectedValue({ code: '23505' });
    (queries.insertOTP as jest.Mock).mockResolvedValue(undefined);

    await userRegistration(req as Request, res as Response);

    expect(queries.createUser).toHaveBeenCalled();
    expect(queries.insertOTP).toHaveBeenCalled();
    expect(status).toHaveBeenCalledWith(200);
    expect(json).toHaveBeenCalledWith({
      message: 'Your OTP has been generated and sent to your whatsapp number',
    });
  });

  it('should return 500 on unexpected database error during user creation', async () => {
    (phoneNumberValidator.default as jest.Mock).mockReturnValue(true);
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    (otpGenerator.default as jest.Mock).mockReturnValue('654321');
    (queries.createUser as jest.Mock).mockRejectedValue(new Error('Unexpected DB error'));

    await userRegistration(req as Request, res as Response);

    expect(status).toHaveBeenCalledWith(500);
    expect(json).toHaveBeenCalledWith({ error: 'Internal Server Error' });
  });

  it('should return 500 on error when inserting OTP', async () => {
    (phoneNumberValidator.default as jest.Mock).mockReturnValue(true);
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    (otpGenerator.default as jest.Mock).mockReturnValue('654321');
    (queries.createUser as jest.Mock).mockResolvedValue(undefined);
    (queries.insertOTP as jest.Mock).mockRejectedValue(new Error('DB error'));

    await userRegistration(req as Request, res as Response);

    expect(status).toHaveBeenCalledWith(500);
    expect(json).toHaveBeenCalledWith({ error: 'Internal Server Error' });
  });
});
