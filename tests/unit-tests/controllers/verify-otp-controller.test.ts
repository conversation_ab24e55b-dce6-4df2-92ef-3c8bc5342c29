import { Request, Response } from 'express';
import verifyOTP from '../../../src/controllers/verify-otp-controller';
import * as queries from '../../../src/database/queries';

jest.mock('../../../src/database/queries');

describe('Verify OTP Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let json: jest.Mock;
  let status: jest.Mock;

  beforeEach(() => {
    req = {
      body: {
        phone_number: '+911234567890',
        otp: '123456',
      },
    };
    json = jest.fn();
    status = jest.fn(() => ({ json }));
    res = {
      status,
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return 400 if phone_number is missing', async () => {
    delete req.body.phone_number;
    await verifyOTP(req as Request, res as Response);
    expect(status).toHaveBeenCalledWith(400);
    expect(json).toHaveBeenCalledWith({ error: 'Phone number and OTP are required.' });
  });

  it('should return 400 if otp is missing', async () => {
    delete req.body.otp;
    await verifyOTP(req as Request, res as Response);
    expect(status).toHaveBeenCalledWith(400);
    expect(json).toHaveBeenCalledWith({ error: 'Phone number and OTP are required.' });
  });

  it('should return 400 if stored OTP does not exist', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue(null);
    await verifyOTP(req as Request, res as Response);
    expect(status).toHaveBeenCalledWith(400);
    expect(json).toHaveBeenCalledWith({ error: 'Invalid or expired OTP.' });
  });

  it('should return 400 if OTP does not match', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue({
      otp_data: { otp_code: '654321' },
    });
    await verifyOTP(req as Request, res as Response);
    expect(status).toHaveBeenCalledWith(400);
    expect(json).toHaveBeenCalledWith({ error: 'Invalid or expired OTP.' });
  });

  it('should return 200 on successful OTP verification', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockResolvedValue({
      otp_data: { otp_code: '123456' },
    });
    await verifyOTP(req as Request, res as Response);
    expect(status).toHaveBeenCalledWith(200);
    expect(json).toHaveBeenCalledWith({ message: 'Authentication successful.' });
  });

  it('should return 500 on database error', async () => {
    (queries.retrieveValidOTP as jest.Mock).mockRejectedValue(new Error('DB error'));
    await verifyOTP(req as Request, res as Response);
    expect(status).toHaveBeenCalledWith(500);
    expect(json).toHaveBeenCalledWith({ error: 'Internal server error.' });
  });
});
