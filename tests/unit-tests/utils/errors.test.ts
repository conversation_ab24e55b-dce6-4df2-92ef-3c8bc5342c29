import { AppError, InternalServerError } from '../../../src/utils/errors';

describe('Custom Errors', () => {
  describe('AppError', () => {
    it('should create an instance of AppError with the correct message and statusCode', () => {
      const errorMessage = 'Test error';
      const statusCode = 404;
      const error = new AppError(errorMessage, statusCode);

      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe(errorMessage);
      expect(error.statusCode).toBe(statusCode);
    });
  });

  describe('InternalServerError', () => {
    it('should create an instance of InternalServerError with a default message and statusCode 500', () => {
      const error = new InternalServerError();

      expect(error).toBeInstanceOf(InternalServerError);
      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Internal Server Error');
      expect(error.statusCode).toBe(500);
    });

    it('should create an instance of InternalServerError with a custom message', () => {
      const customMessage = 'A custom internal server error occurred.';
      const error = new InternalServerError(customMessage);

      expect(error.message).toBe(customMessage);
      expect(error.statusCode).toBe(500);
    });
  });
});
