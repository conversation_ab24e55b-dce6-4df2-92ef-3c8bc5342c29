import generateOTP from '../../../src/utils/otp-generator';

describe('OTP Generator', () => {
  it('should generate a 4-digit OTP', () => {
    const otp = generateOTP();
    expect(otp).toHaveLength(4);
  });

  it('should generate an OTP containing only digits', () => {
    const otp = generateOTP();
    expect(otp).toMatch(/^[0-9]{4}$/);
  });

  it('should generate different OTPs on subsequent calls', () => {
    const otp1 = generateOTP();
    const otp2 = generateOTP();
    // While there's a tiny chance of collision, it's highly unlikely in a test scenario.
    expect(otp1).not.toEqual(otp2);
  });
});
