import isValidPhoneNumber from '../../../src/utils/phone-number-validator';

describe('Phone Number Validator', () => {
  it('should return true for a valid phone number', () => {
    expect(isValidPhoneNumber('+911234567890')).toBe(true);
  });

  it('should return true for a valid phone number with spaces', () => {
    expect(isValidPhoneNumber(' +91 ************ ')).toBe(true);
  });

  it('should return false for a phone number without the country code', () => {
    expect(isValidPhoneNumber('1234567890')).toBe(false);
  });

  it('should return false for a phone number with an incorrect country code', () => {
    expect(isValidPhoneNumber('+921234567890')).toBe(false);
  });

  it('should return false for a phone number with too few digits', () => {
    expect(isValidPhoneNumber('+91123456789')).toBe(false);
  });

  it('should return false for a phone number with too many digits', () => {
    expect(isValidPhoneNumber('+9112345678901')).toBe(false);
  });

  it('should return false for a phone number with non-digit characters', () => {
    expect(isValidPhoneNumber('+9112345a7890')).toBe(false);
  });

  it('should return false for an empty string', () => {
    expect(isValidPhoneNumber('')).toBe(false);
  });

  it('should return false for a string with only whitespace', () => {
    expect(isValidPhoneNumber('  ')).toBe(false);
  });
});
