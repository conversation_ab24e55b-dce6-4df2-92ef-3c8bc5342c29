{
  "compilerOptions": {
    "target": "ES2020",                         // Compile TypeScript down to ES2020 JavaScript
    "module": "NodeNext",                       // Use Node.js ESM/CommonJS hybrid module system
    "moduleResolution": "NodeNext",             // Resolve modules using Node.js rules (supports ESM + CommonJS)
    "rootDir": ".",                             // Root folder of TypeScript source files
    "outDir": "./dist",                         // Output directory for compiled JavaScript
    "esModuleInterop": true,                    // Allow default imports from CommonJS modules
    "resolveJsonModule": true,                  // Enable importing .json files as modules
    "strict": true,                             // Enable all strict type-checking options
    "noImplicitAny": true,                      // Error on variables with implicit 'any' type
    "noImplicitThis": true,                     // Error on 'this' expressions with implicit 'any'
    "noUnusedLocals": true,                     // Error on unused local variables
    "noUnusedParameters": true,                 // Error on unused function parameters
    "noFallthroughCasesInSwitch": true,         // Error if switch cases fall through without 'break'
    "forceConsistentCasingInFileNames": true ,   // Ensure file name casing consistency between import and file system
    "isolatedModules": true                     // Compile each file in isolation (required for ESM + Babel/Jest)
  },
  "include": ["src", "config","migrations"],                 // Folders included in compilation
  "exclude": ["node_modules", "dist"]           // Folders excluded from compilation
}
